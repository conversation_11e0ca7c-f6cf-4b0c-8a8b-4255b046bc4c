"use client"

import { useEffect, useRef, useState } from "react"
import { Play, Pause, VolumeX, Volume2, ChevronRight, ChevronLeft } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import ClimateChart from "@/components/climate-chart"
import CO2Visualization from "@/components/co2-visualization"
import WeatherDisplay from "@/components/weather-display"
import TemperatureComparison from "@/components/temperature-comparison"
import <PERSON><PERSON>haracter from "@/components/sam-character"
import ClimateAnomaly from "@/components/climate-anomaly"
import SolutionsVisualization from "@/components/solutions-visualization"
import SlideBackground from "@/components/slide-background"
import LandPage from "../land-page/LandPage";

export default function Page() {
  return <LandPage />;
}
