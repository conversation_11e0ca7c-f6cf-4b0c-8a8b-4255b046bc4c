/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', sans-serif;
    background: #000;
    color: #ffffff;
    overflow-x: hidden;
}

/* Hero Section */
.hero-section {
    height: 100vh;
    position: relative;
    overflow: hidden;
}

.container {
    display: flex;
    height: 100vh;
    width: 100vw;
    position: relative;
}

.title {
    position: absolute;
    top: 50%;
    left: 50px;
    transform: translateY(-50%);
    font-size: 4rem;
    font-weight: 700;
    color: #ffffff;
    z-index: 10;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    background: linear-gradient(135deg, #64ffda 0%, #4fc3f7 50%, #29b6f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.spline-container {
    width: 100%;
    height: 100%;
    position: relative;
}

.spline-container iframe {
    width: 100%;
    height: 100%;
    border: none;
}

/* Explore Earth Button */
.explore-earth-btn {
    position: absolute;
    bottom: 40px;
    left: 50%;
    transform: translateX(-50%);
    background: transparent;
    color: #64ffda;
    border: 2px solid #64ffda;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    z-index: 10;
    animation: bounce 2s infinite;
}

.explore-earth-btn:hover {
    background: #64ffda;
    color: #000;
    transform: translateX(-50%) translateY(-3px);
}

.explore-earth-btn i {
    font-size: 1rem;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Data Section */
.data-section {
    min-height: 100vh;
    background: linear-gradient(180deg, #000 0%, #1a1a2e 50%, #16213e 100%);
    padding: 100px 20px;
    position: relative;
}

.data-container {
    max-width: 1400px;
    margin: 0 auto;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #64ffda 0%, #4fc3f7 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #b0bec5;
    max-width: 600px;
    margin: 0 auto;
}

.data-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.data-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(100, 255, 218, 0.2);
    transition: all 0.3s ease;
    text-align: center;
}

.data-card:hover {
    transform: translateY(-10px);
    border-color: rgba(100, 255, 218, 0.4);
    box-shadow: 0 20px 40px rgba(100, 255, 218, 0.1);
}

.data-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, #64ffda 0%, #4fc3f7 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.data-icon i {
    font-size: 2rem;
    color: #000;
}

.data-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    color: #ffffff;
}

.data-stats {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.stat-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 1rem;
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-number {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    color: #64ffda;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #b0bec5;
}

/* Features Section */
.features-section {
    min-height: 100vh;
    background: linear-gradient(180deg, #16213e 0%, #0f3460 50%, #000 100%);
    padding: 100px 20px;
}

.features-container {
    max-width: 1400px;
    margin: 0 auto;
}

.features-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    background: linear-gradient(135deg, #64ffda 0%, #4fc3f7 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.feature-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    text-align: center;
}

.feature-item:hover {
    transform: translateY(-10px);
    border-color: rgba(100, 255, 218, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.feature-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, #64ffda 0%, #4fc3f7 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feature-icon i {
    font-size: 1.8rem;
    color: #000;
}

.feature-item h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #ffffff;
}

.feature-item p {
    color: #b0bec5;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.feature-data {
    background: rgba(100, 255, 218, 0.1);
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    border: 1px solid rgba(100, 255, 218, 0.3);
    display: inline-block;
}

.feature-data span {
    color: #64ffda;
    font-weight: 600;
    font-size: 0.9rem;
}

/* Call to Action Section */
.cta-section {
    background: linear-gradient(135deg, #000 0%, #1a1a2e 50%, #16213e 100%);
    padding: 100px 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle cx="200" cy="200" r="2" fill="%23ffffff" opacity="0.3"/><circle cx="800" cy="300" r="1" fill="%23ffffff" opacity="0.5"/><circle cx="400" cy="600" r="1.5" fill="%23ffffff" opacity="0.4"/><circle cx="900" cy="700" r="1" fill="%23ffffff" opacity="0.3"/><circle cx="100" cy="800" r="2" fill="%23ffffff" opacity="0.6"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.cta-container {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.cta-content {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 30px;
    padding: 4rem 3rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(100, 255, 218, 0.2);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #64ffda 0%, #4fc3f7 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.cta-description {
    font-size: 1.2rem;
    color: #b0bec5;
    margin-bottom: 2.5rem;
    line-height: 1.6;
}

.get-started-btn {
    background: linear-gradient(135deg, #64ffda 0%, #4fc3f7 100%);
    color: #000;
    border: none;
    padding: 18px 40px;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(100, 255, 218, 0.3);
}

.get-started-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(100, 255, 218, 0.4);
    background: linear-gradient(135deg, #4fc3f7 0%, #64ffda 100%);
}

.get-started-btn i {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.get-started-btn:hover i {
    transform: translateX(3px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .title {
        font-size: 2.5rem;
        left: 20px;
        right: 20px;
        text-align: center;
        top: 15%;
        transform: translateY(0);
    }

    .explore-earth-btn {
        bottom: 20px;
        padding: 12px 25px;
        font-size: 0.9rem;
    }

    .section-title {
        font-size: 2.2rem;
    }

    .features-title {
        font-size: 2rem;
    }

    .cta-title {
        font-size: 2rem;
    }

    .cta-content {
        padding: 3rem 2rem;
    }

    .get-started-btn {
        padding: 15px 30px;
        font-size: 1rem;
    }

    .data-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .data-section,
    .features-section,
    .cta-section {
        padding: 60px 20px;
    }
}

@media (max-width: 480px) {
    .title {
        font-size: 2rem;
        top: 10%;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .features-title {
        font-size: 1.6rem;
    }

    .cta-title {
        font-size: 1.8rem;
    }

    .cta-content {
        padding: 2.5rem 1.5rem;
    }
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.6s ease;
}

.slide-in-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s ease;
}

.slide-in-right.visible {
    opacity: 1;
    transform: translateX(0);
}


