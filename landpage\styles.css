/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: #000;
    color: #ffffff;
    overflow: hidden;
    height: 100vh;
}

.container {
    display: flex;
    height: 100vh;
    width: 100vw;
}

.title {
    position: absolute;
    top: 50%;
    left: 50px;
    transform: translateY(-50%);
    font-size: 4rem;
    font-weight: 700;
    color: #ffffff;
    z-index: 10;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    background: linear-gradient(135deg, #64ffda 0%, #4fc3f7 50%, #29b6f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.spline-container {
    width: 100%;
    height: 100%;
    position: relative;
}

.spline-container iframe {
    width: 100%;
    height: 100%;
    border: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .title {
        font-size: 2.5rem;
        left: 20px;
        right: 20px;
        text-align: center;
        top: 10%;
        transform: translateY(0);
    }
}


