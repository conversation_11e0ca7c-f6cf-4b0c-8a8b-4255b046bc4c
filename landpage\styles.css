/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Montserrat', 'Poppins', sans-serif;
    background: #0a0a0a;
    color: #ffffff;
    overflow-x: hidden;
    line-height: 1.6;
}

/* NASA Color Palette */
:root {
    --nasa-blue: #0B3D91;
    --nasa-red: #FC3D21;
    --nasa-white: #FFFFFF;
    --space-dark: #0a0a0a;
    --space-blue: #1e3a8a;
    --accent-cyan: #00d4ff;
    --text-light: #e5e7eb;
    --text-muted: #9ca3af;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(15px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--accent-cyan);
    font-family: 'Montserrat', sans-serif;
}

.nav-logo i {
    font-size: 1.6rem;
    color: var(--nasa-red);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2.5rem;
}

.nav-menu a {
    color: var(--text-light);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 0;
}

.nav-menu a:hover {
    color: var(--accent-cyan);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--nasa-red), var(--accent-cyan));
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--accent-cyan);
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

/* Hero Section */
.hero-section {
    height: 100vh;
    position: relative;
    overflow: hidden;
}

.spline-container {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 1;
}

.spline-container iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.hero-text-overlay {
    position: absolute;
    top: 50%;
    left: 50px;
    transform: translateY(-50%);
    z-index: 10;
    max-width: 500px;
}

.hero-content {
    background: transparent;
    backdrop-filter: none;
    border-radius: 20px;
    padding: 2.5rem;
    border: none;
    box-shadow: none;
}

.hero-title {
    font-size: 2.8rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--accent-cyan) 0%, var(--nasa-blue) 50%, var(--nasa-red) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-family: 'Montserrat', sans-serif;
    line-height: 1.2;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.hero-subtitle {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--accent-cyan);
    margin-bottom: 1.5rem;
    font-family: 'Poppins', sans-serif;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
}

.hero-description {
    font-size: 1rem;
    color: var(--text-light);
    margin-bottom: 2rem;
    line-height: 1.6;
    font-family: 'Poppins', sans-serif;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
}

.cta-button {
    background: linear-gradient(135deg, var(--nasa-red) 0%, var(--accent-cyan) 100%);
    color: var(--nasa-white);
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(252, 61, 33, 0.3);
    font-family: 'Montserrat', sans-serif;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(252, 61, 33, 0.4);
    background: linear-gradient(135deg, var(--accent-cyan) 0%, var(--nasa-red) 100%);
}

.cta-button i {
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.cta-button:hover i {
    transform: translateX(3px);
}

/* Common Section Styles */
.section-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.8rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--accent-cyan) 0%, var(--nasa-blue) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-family: 'Montserrat', sans-serif;
}

.title-underline {
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--nasa-red), var(--accent-cyan));
    margin: 0 auto;
    border-radius: 2px;
}

/* About Us Section */
.about-section {
    min-height: 100vh;
    background: linear-gradient(180deg, var(--space-dark) 0%, var(--space-blue) 50%, var(--space-dark) 100%);
    padding: 120px 0;
    position: relative;
    margin-top: -100px;
    z-index: 10;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 3rem;
    align-items: center;
}

.about-description {
    font-size: 1.3rem;
    color: var(--text-light);
    line-height: 1.8;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    font-family: 'Poppins', sans-serif;
}

.team-icons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.team-member {
    text-align: center;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 212, 255, 0.2);
    transition: all 0.3s ease;
}

.team-member:hover {
    transform: translateY(-10px);
    border-color: rgba(0, 212, 255, 0.4);
    box-shadow: 0 20px 40px rgba(0, 212, 255, 0.1);
}

.member-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1rem;
    background: linear-gradient(135deg, var(--nasa-red) 0%, var(--accent-cyan) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.member-icon i {
    font-size: 2rem;
    color: var(--nasa-white);
}

.team-member span {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--accent-cyan);
    font-family: 'Montserrat', sans-serif;
}

/* Challenge Section */
.challenge-section {
    min-height: 100vh;
    background: linear-gradient(180deg, var(--space-blue) 0%, var(--space-dark) 50%, var(--nasa-blue) 100%);
    padding: 120px 0;
    position: relative;
}

.challenge-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.challenge-description {
    font-size: 1.2rem;
    color: var(--text-light);
    line-height: 1.8;
    margin-bottom: 3rem;
    font-family: 'Poppins', sans-serif;
}

.challenge-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat-box {
    text-align: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 212, 255, 0.2);
    flex: 1;
}

.stat-box .stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--nasa-red);
    margin-bottom: 0.5rem;
    font-family: 'Montserrat', sans-serif;
}

.stat-box .stat-label {
    font-size: 0.9rem;
    color: var(--text-muted);
    font-weight: 500;
}

.nasa-button {
    background: linear-gradient(135deg, var(--nasa-blue) 0%, var(--accent-cyan) 100%);
    color: var(--nasa-white);
    text-decoration: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 10px 25px rgba(11, 61, 145, 0.3);
    font-family: 'Montserrat', sans-serif;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.nasa-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(11, 61, 145, 0.4);
    background: linear-gradient(135deg, var(--accent-cyan) 0%, var(--nasa-blue) 100%);
}

.nasa-button i {
    font-size: 1rem;
}

.challenge-action {
    text-align: center;
}

/* Terra History Section */
.terra-section {
    min-height: 100vh;
    background: linear-gradient(180deg, var(--nasa-blue) 0%, var(--space-dark) 50%, var(--space-blue) 100%);
    padding: 120px 0;
    position: relative;
}

.terra-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 4rem;
}

.terra-timeline {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
}

.timeline-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 212, 255, 0.2);
    transition: all 0.3s ease;
    text-align: center;
}

.timeline-item:hover {
    transform: translateY(-10px);
    border-color: rgba(252, 61, 33, 0.4);
    box-shadow: 0 20px 40px rgba(252, 61, 33, 0.1);
}

.timeline-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, var(--nasa-red) 0%, var(--accent-cyan) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.timeline-icon i {
    font-size: 1.8rem;
    color: var(--nasa-white);
}

.timeline-content h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--accent-cyan);
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
}

.timeline-content p {
    color: var(--text-light);
    line-height: 1.6;
    font-family: 'Poppins', sans-serif;
}

.terra-instruments h3 {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 2rem;
    color: var(--accent-cyan);
    font-family: 'Montserrat', sans-serif;
}

.instruments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.instrument-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 212, 255, 0.2);
    transition: all 0.3s ease;
    text-align: center;
}

.instrument-card:hover {
    transform: translateY(-5px);
    border-color: rgba(0, 212, 255, 0.4);
    box-shadow: 0 15px 30px rgba(0, 212, 255, 0.1);
}

.instrument-icon {
    width: 50px;
    height: 50px;
    margin: 0 auto 1rem;
    background: linear-gradient(135deg, var(--nasa-blue) 0%, var(--accent-cyan) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.instrument-icon i {
    font-size: 1.2rem;
    color: var(--nasa-white);
}

.instrument-card h4 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: var(--nasa-red);
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
}

.instrument-card p {
    font-size: 0.85rem;
    color: var(--text-muted);
    line-height: 1.4;
}

/* Final CTA Section */
.final-cta-section {
    background: linear-gradient(135deg, var(--space-dark) 0%, var(--nasa-blue) 50%, var(--space-dark) 100%);
    padding: 120px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.final-cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle cx="200" cy="200" r="2" fill="%23ffffff" opacity="0.3"/><circle cx="800" cy="300" r="1" fill="%23ffffff" opacity="0.5"/><circle cx="400" cy="600" r="1.5" fill="%23ffffff" opacity="0.4"/><circle cx="900" cy="700" r="1" fill="%23ffffff" opacity="0.3"/><circle cx="100" cy="800" r="2" fill="%23ffffff" opacity="0.6"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.cta-container {
    max-width: 900px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
    padding: 0 2rem;
}

.cta-content {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 30px;
    padding: 4rem 3rem;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(0, 212, 255, 0.2);
    box-shadow: 0 25px 70px rgba(0, 0, 0, 0.3);
}

.cta-title {
    font-size: 2.8rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, var(--accent-cyan) 0%, var(--nasa-red) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-family: 'Montserrat', sans-serif;
}

.cta-description {
    font-size: 1.3rem;
    color: var(--text-light);
    margin-bottom: 2.5rem;
    line-height: 1.6;
    font-family: 'Poppins', sans-serif;
}

.explore-button {
    background: linear-gradient(135deg, var(--nasa-red) 0%, var(--accent-cyan) 100%);
    color: var(--nasa-white);
    border: none;
    padding: 20px 45px;
    border-radius: 50px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 15px 40px rgba(252, 61, 33, 0.3);
    font-family: 'Montserrat', sans-serif;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.explore-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 20px 50px rgba(252, 61, 33, 0.4);
    background: linear-gradient(135deg, var(--accent-cyan) 0%, var(--nasa-red) 100%);
}

.explore-button i {
    font-size: 1.3rem;
    transition: transform 0.3s ease;
}

.explore-button:hover i {
    transform: translateX(5px);
}

/* Footer */
.footer {
    background: linear-gradient(180deg, var(--space-dark) 0%, #000000 100%);
    padding: 3rem 0 1rem;
    border-top: 1px solid rgba(0, 212, 255, 0.2);
}

.footer-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    color: var(--accent-cyan);
    margin-bottom: 1.5rem;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
}

.social-links {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--text-light);
    text-decoration: none;
    padding: 0.8rem 1rem;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-link:hover {
    background: rgba(0, 212, 255, 0.1);
    border-color: rgba(0, 212, 255, 0.3);
    color: var(--accent-cyan);
    transform: translateX(5px);
}

.social-link i {
    font-size: 1.2rem;
    width: 20px;
}

.footer-disclaimer {
    color: var(--text-muted);
    line-height: 1.6;
    font-family: 'Poppins', sans-serif;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-muted);
    font-family: 'Poppins', sans-serif;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-text-overlay {
        left: 20px;
        right: 20px;
        max-width: none;
        top: 15%;
        transform: translateY(0);
    }

    .hero-content {
        padding: 2rem;
    }

    .hero-title {
        font-size: 2.2rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .hero-description {
        font-size: 0.9rem;
    }

    .cta-button {
        padding: 12px 25px;
        font-size: 0.9rem;
    }

    .section-title {
        font-size: 2.2rem;
    }

    .challenge-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .challenge-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .terra-timeline {
        grid-template-columns: 1fr;
    }

    .instruments-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .team-icons {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .cta-title {
        font-size: 2.2rem;
    }

    .cta-content {
        padding: 3rem 2rem;
    }

    .about-section,
    .challenge-section,
    .terra-section,
    .final-cta-section {
        padding: 80px 0;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}

@media (max-width: 480px) {
    .hero-text-overlay {
        top: 10%;
    }

    .hero-content {
        padding: 1.5rem;
    }

    .hero-title {
        font-size: 1.8rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .hero-description {
        font-size: 0.85rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .cta-title {
        font-size: 1.8rem;
    }

    .cta-content {
        padding: 2.5rem 1.5rem;
    }

    .cta-button,
    .nasa-button,
    .explore-button {
        padding: 15px 25px;
        font-size: 0.9rem;
    }

    .about-description {
        font-size: 1.1rem;
    }

    .challenge-description {
        font-size: 1rem;
    }
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.6s ease;
}

.slide-in-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s ease;
}

.slide-in-right.visible {
    opacity: 1;
    transform: translateX(0);
}

/* Mobile Navigation */
@media (max-width: 768px) {
    .nav-menu.active {
        display: flex;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: rgba(10, 10, 10, 0.98);
        flex-direction: column;
        padding: 2rem;
        backdrop-filter: blur(15px);
        border-top: 1px solid rgba(0, 212, 255, 0.2);
    }

    .nav-menu.active li {
        margin: 0.5rem 0;
    }

    .nav-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.6s ease;
}

.slide-in-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s ease;
}

.slide-in-right.visible {
    opacity: 1;
    transform: translateX(0);
}


