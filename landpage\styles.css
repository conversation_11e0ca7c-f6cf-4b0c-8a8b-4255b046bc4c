/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #ffffff;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.5rem;
    font-weight: 700;
    color: #64ffda;
}

.nav-logo i {
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    color: #64ffda;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #64ffda;
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: #ffffff;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 120px 20px 80px;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle cx="200" cy="200" r="2" fill="%23ffffff" opacity="0.3"/><circle cx="800" cy="300" r="1" fill="%23ffffff" opacity="0.5"/><circle cx="400" cy="600" r="1.5" fill="%23ffffff" opacity="0.4"/><circle cx="900" cy="700" r="1" fill="%23ffffff" opacity="0.3"/><circle cx="100" cy="800" r="2" fill="%23ffffff" opacity="0.6"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.gradient-text {
    background: linear-gradient(135deg, #64ffda 0%, #4fc3f7 50%, #29b6f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 1.2rem;
    color: #b0bec5;
    margin-bottom: 2rem;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #64ffda 0%, #4fc3f7 100%);
    color: #0a0a0a;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(100, 255, 218, 0.3);
}

.btn-secondary {
    background: transparent;
    color: #64ffda;
    border: 2px solid #64ffda;
}

.btn-secondary:hover {
    background: #64ffda;
    color: #0a0a0a;
    transform: translateY(-2px);
}

.hero-stats {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.stat {
    text-align: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #64ffda;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #b0bec5;
}

/* Model Section */
.model-section {
    padding: 100px 20px;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 0%, rgba(26, 26, 46, 0.9) 100%);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #64ffda 0%, #4fc3f7 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-description {
    font-size: 1.1rem;
    color: #b0bec5;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.8;
}

.model-container {
    max-width: 1400px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 3fr 1fr;
    gap: 3rem;
    align-items: start;
}

.model-wrapper {
    position: relative;
    width: 100%;
    height: 700px;
    border-radius: 20px;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.5);
    border: 2px solid rgba(100, 255, 218, 0.2);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.model-wrapper iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.model-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 1rem;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.loading-spinner {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: #64ffda;
    font-size: 1.1rem;
}

.loading-spinner i {
    font-size: 2rem;
}

.model-controls {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    height: fit-content;
}

.control-panel h3 {
    color: #64ffda;
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
}

.controls-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.control-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    transition: background 0.3s ease;
}

.control-item:hover {
    background: rgba(100, 255, 218, 0.1);
}

.control-item i {
    color: #64ffda;
    font-size: 1.2rem;
    width: 20px;
}

.control-item span {
    color: #e0e0e0;
    font-size: 0.9rem;
}

/* Features Section */
.features {
    padding: 100px 20px;
    background: linear-gradient(180deg, rgba(26, 26, 46, 0.9) 0%, rgba(0, 0, 0, 0.8) 100%);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
    border-color: rgba(100, 255, 218, 0.3);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, #64ffda 0%, #4fc3f7 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.feature-icon i {
    font-size: 2rem;
    color: #0a0a0a;
}

.feature-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #ffffff;
}

.feature-card p {
    color: #b0bec5;
    line-height: 1.6;
}

/* Footer */
.footer {
    background: rgba(0, 0, 0, 0.9);
    padding: 3rem 0 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.5rem;
    font-weight: 700;
    color: #64ffda;
    margin-bottom: 1rem;
}

.footer-section h4 {
    color: #ffffff;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #b0bec5;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #64ffda;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #b0bec5;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #64ffda;
    color: #0a0a0a;
    transform: translateY(-2px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: #b0bec5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        justify-content: center;
    }
    
    .hero-stats {
        flex-direction: row;
        justify-content: center;
    }
    
    .model-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .model-wrapper {
        height: 500px;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
}
