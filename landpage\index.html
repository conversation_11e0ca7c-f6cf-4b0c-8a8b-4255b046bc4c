<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NASA Space Apps</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Hero Section with Spline Animation -->
    <section class="hero-section">
        <div class="container">
            <h1 class="title">NASA Space Apps</h1>
            <div class="spline-container">
                <iframe
                    src="https://my.spline.design/earthdayandnight-E2T4GItNWEUxvQSCGkWTaV3E/"
                    frameborder="0"
                    width="100%"
                    height="100%"
                    allowfullscreen
                    title="Earth Day and Night 3D Model">
                </iframe>
            </div>
            <button class="explore-earth-btn" onclick="scrollToData()">
                <i class="fas fa-globe"></i>
                <span>Explore Earth</span>
                <i class="fas fa-arrow-down"></i>
            </button>
        </div>
    </section>

    <!-- Earth Data Section -->
    <section class="data-section" id="data-section">
        <div class="data-container">
            <div class="section-header">
                <h2 class="section-title">Earth Data & Statistics</h2>
                <p class="section-subtitle">Discover fascinating facts about our planet</p>
            </div>

            <div class="data-grid">
                <div class="data-card">
                    <div class="data-icon">
                        <i class="fas fa-globe-americas"></i>
                    </div>
                    <h3>Planet Earth</h3>
                    <div class="data-stats">
                        <div class="stat-item">
                            <span class="stat-number">12,742</span>
                            <span class="stat-label">km Diameter</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">510.1</span>
                            <span class="stat-label">Million km² Surface</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">71%</span>
                            <span class="stat-label">Water Coverage</span>
                        </div>
                    </div>
                </div>

                <div class="data-card">
                    <div class="data-icon">
                        <i class="fas fa-sun"></i>
                    </div>
                    <h3>Day & Night Cycle</h3>
                    <div class="data-stats">
                        <div class="stat-item">
                            <span class="stat-number">24</span>
                            <span class="stat-label">Hours Rotation</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">1,670</span>
                            <span class="stat-label">km/h at Equator</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">23.5°</span>
                            <span class="stat-label">Axial Tilt</span>
                        </div>
                    </div>
                </div>

                <div class="data-card">
                    <div class="data-icon">
                        <i class="fas fa-thermometer-half"></i>
                    </div>
                    <h3>Climate Data</h3>
                    <div class="data-stats">
                        <div class="stat-item">
                            <span class="stat-number">15°C</span>
                            <span class="stat-label">Average Temperature</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">421</span>
                            <span class="stat-label">ppm CO₂ Level</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">1.1°C</span>
                            <span class="stat-label">Warming Since 1880</span>
                        </div>
                    </div>
                </div>

                <div class="data-card">
                    <div class="data-icon">
                        <i class="fas fa-satellite"></i>
                    </div>
                    <h3>Space Perspective</h3>
                    <div class="data-stats">
                        <div class="stat-item">
                            <span class="stat-number">149.6</span>
                            <span class="stat-label">Million km from Sun</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">365.25</span>
                            <span class="stat-label">Days per Year</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">4.54</span>
                            <span class="stat-label">Billion Years Old</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Features Section -->
    <section class="features-section">
        <div class="features-container">
            <h2 class="features-title">Explore Earth's Phenomena</h2>
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3>Time Zones</h3>
                    <p>Understand how Earth's rotation creates 24 different time zones across the globe</p>
                    <div class="feature-data">
                        <span>24 Time Zones</span>
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-leaf"></i>
                    </div>
                    <h3>Seasons</h3>
                    <p>Discover how Earth's axial tilt creates the four seasons we experience</p>
                    <div class="feature-data">
                        <span>23.5° Tilt Angle</span>
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-water"></i>
                    </div>
                    <h3>Ocean Currents</h3>
                    <p>Learn about the massive water circulation systems that regulate our climate</p>
                    <div class="feature-data">
                        <span>5 Major Gyres</span>
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-wind"></i>
                    </div>
                    <h3>Atmosphere</h3>
                    <p>Explore the layers of atmosphere that protect and sustain life on Earth</p>
                    <div class="feature-data">
                        <span>5 Atmospheric Layers</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="cta-section">
        <div class="cta-container">
            <div class="cta-content">
                <h2 class="cta-title">Ready to Start Your Space Journey?</h2>
                <p class="cta-description">Join NASA Space Apps and explore the universe with cutting-edge tools and data</p>
                <button class="get-started-btn" onclick="redirectToLogin()">
                    <span>Get Started</span>
                    <i class="fas fa-rocket"></i>
                </button>
            </div>
        </div>
    </section>

    <script src="script.js"></script>
</body>
</html>
