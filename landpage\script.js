// Enhanced script for the NASA Space Apps landing page
document.addEventListener('DOMContentLoaded', function() {
    console.log('NASA Space Apps landing page loaded');

    // Initialize animations
    initScrollAnimations();
    initCounterAnimations();

    // Add smooth scrolling behavior
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

// Function to scroll to data section
function scrollToData() {
    const dataSection = document.querySelector('#data-section');
    if (dataSection) {
        dataSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Function to redirect to login page
function redirectToLogin() {
    // You can replace this URL with your actual login page
    window.location.href = 'login.html';
    // Or if you want to open in a new tab:
    // window.open('login.html', '_blank');
}

// Initialize scroll animations
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);

    // Add animation classes to elements
    const dataCards = document.querySelectorAll('.data-card');
    dataCards.forEach((card, index) => {
        card.classList.add('fade-in');
        card.style.transitionDelay = `${index * 0.1}s`;
        observer.observe(card);
    });

    const featureItems = document.querySelectorAll('.feature-item');
    featureItems.forEach((item, index) => {
        item.classList.add('fade-in');
        item.style.transitionDelay = `${index * 0.1}s`;
        observer.observe(item);
    });

    // Animate section headers
    const sectionHeaders = document.querySelectorAll('.section-header, .features-title');
    sectionHeaders.forEach(header => {
        header.classList.add('slide-in-left');
        observer.observe(header);
    });
}

// Initialize counter animations
function initCounterAnimations() {
    const counterObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                counterObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(stat => {
        counterObserver.observe(stat);
    });
}

// Counter animation function
function animateCounter(element) {
    const text = element.textContent;
    const number = parseFloat(text.replace(/[^\d.]/g, ''));
    const suffix = text.replace(/[\d.]/g, '');

    if (isNaN(number)) return;

    const duration = 2000;
    const steps = 60;
    const increment = number / steps;
    let current = 0;

    const timer = setInterval(() => {
        current += increment;
        if (current >= number) {
            current = number;
            clearInterval(timer);
        }

        if (suffix.includes('°')) {
            element.textContent = current.toFixed(1) + suffix;
        } else if (number < 10) {
            element.textContent = current.toFixed(2) + suffix;
        } else if (number < 100) {
            element.textContent = current.toFixed(1) + suffix;
        } else {
            element.textContent = Math.floor(current) + suffix;
        }
    }, duration / steps);
}

// Add parallax effect to hero section
window.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const heroSection = document.querySelector('.hero-section');

    if (heroSection && scrolled < window.innerHeight) {
        const rate = scrolled * 0.5;
        heroSection.style.transform = `translateY(${rate}px)`;
    }
});

// Add hover effects to data cards
document.querySelectorAll('.data-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-15px) scale(1.02)';
    });

    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(-10px) scale(1)';
    });
});

// Add hover effects to feature items
document.querySelectorAll('.feature-item').forEach(item => {
    item.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-15px) scale(1.02)';
    });

    item.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(-10px) scale(1)';
    });
});

// Add floating animation to buttons
function addFloatingAnimation() {
    const exploreBtn = document.querySelector('.explore-earth-btn');
    const getStartedBtn = document.querySelector('.get-started-btn');

    if (exploreBtn) {
        exploreBtn.addEventListener('mouseenter', function() {
            this.style.animation = 'none';
        });

        exploreBtn.addEventListener('mouseleave', function() {
            this.style.animation = '';
        });
    }

    // Add hover effect to CTA section get started button
    if (getStartedBtn) {
        getStartedBtn.addEventListener('mouseenter', function() {
            this.querySelector('i').style.transform = 'translateX(5px)';
        });

        getStartedBtn.addEventListener('mouseleave', function() {
            this.querySelector('i').style.transform = 'translateX(0)';
        });
    }
}

// Initialize floating animations
addFloatingAnimation();

// Performance optimization: Throttle scroll events
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Apply throttling to scroll events
const throttledScroll = throttle(function() {
    const scrolled = window.pageYOffset;
    const heroSection = document.querySelector('.hero-section');

    if (heroSection && scrolled < window.innerHeight) {
        const rate = scrolled * 0.3;
        heroSection.style.transform = `translateY(${rate}px)`;
    }
}, 16); // ~60fps

window.addEventListener('scroll', throttledScroll);
